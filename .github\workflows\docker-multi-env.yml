name: Multi-Environment Docker Build

on:
  push:
    branches: [ main, develop, staging ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment'
        required: true
        default: 'development'
        type: choice
        options:
        - development
        - staging
        - production

env:
  REGISTRY: ghcr.io

jobs:
  build-matrix:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        include:
          - environment: development
            dockerfile: Dockerfile
            image_suffix: -dev
            node_env: development
          - environment: staging
            dockerfile: Dockerfile
            image_suffix: -staging
            node_env: staging
          - environment: production
            dockerfile: Dockerfile
            image_suffix: ""
            node_env: production

    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set environment variables
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "TARGET_ENV=${{ github.event.inputs.environment }}" >> $GITHUB_ENV
          elif [ "${{ github.ref }}" = "refs/heads/main" ]; then
            echo "TARGET_ENV=production" >> $GITHUB_ENV
          elif [ "${{ github.ref }}" = "refs/heads/staging" ]; then
            echo "TARGET_ENV=staging" >> $GITHUB_ENV
          else
            echo "TARGET_ENV=development" >> $GITHUB_ENV
          fi

      - name: Skip if not target environment
        if: matrix.environment != env.TARGET_ENV
        run: |
          echo "Skipping ${{ matrix.environment }} build (target: ${{ env.TARGET_ENV }})"
          exit 0

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}${{ matrix.image_suffix }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest
            type=raw,value=${{ matrix.environment }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./${{ matrix.dockerfile }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=${{ matrix.environment }}
          cache-to: type=gha,mode=max,scope=${{ matrix.environment }}
          platforms: linux/amd64,linux/arm64
          build-args: |
            NODE_ENV=${{ matrix.node_env }}
            BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VCS_REF=${{ github.sha }}
            ENVIRONMENT=${{ matrix.environment }}

      - name: Output build summary
        run: |
          echo "## 🐳 Docker Build Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: ${{ matrix.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Image**: ${{ env.REGISTRY }}/${{ github.repository }}${{ matrix.image_suffix }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Tags**: ${{ steps.meta.outputs.tags }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Dockerfile**: ${{ matrix.dockerfile }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Node Environment**: ${{ matrix.node_env }}" >> $GITHUB_STEP_SUMMARY
