name: CI Frontend Build and Tests

on:
  pull_request:
    branches: [ main, stage ]

jobs:
  ci-frontend:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build frontend
        run: npm run frontend:ci

      - name: Run frontend tests
        run: cd client && npm run test:ci
