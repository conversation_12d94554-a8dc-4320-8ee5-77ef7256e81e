name: CI Backend Build and Tests

on:
  pull_request:
    branches: [ main, stage ]

jobs:
  ci-backend:
    runs-on: ubuntu-latest
    timeout-minutes: 30

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 22
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Data Provider Package
        run: npm run build:data-provider

      - name: Install Data Schemas Package
        run: npm run build:data-schemas

      - name: Install API Package
        run: npm run build:api

      - name: Build project
        run: npm run frontend

      - name: Run data-provider tests
        run: cd packages/data-provider && npm run test:ci

      - name: Run data-schemas tests
        run: cd packages/data-schemas && npm run test:ci

      - name: Run api tests
        run: cd packages/api && npm run test:ci