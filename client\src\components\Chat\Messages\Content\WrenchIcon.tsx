import React, { useState, useEffect } from 'react';

export default function WrenchIcon() {
  const [rotate, setRotate] = useState(false);

  useEffect(() => {
    const timer = setInterval(() => {
      setRotate((r) => !r);
    }, 2000); // Change 2000 to the duration you want for each pause

    return () => clearInterval(timer);
  }, []);

  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      xmlnsXlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 24 24"
      width="24"
      height="24"
      style={{ width: '100%', height: '100%', transform: 'rotate(30deg)' }}
      preserveAspectRatio="xMidYMid meet"
    >
      <defs>
        <clipPath id="__lottie_element_28">
          <rect width="24" height="24" x="0" y="0"></rect>
        </clipPath>
      </defs>
      <g clipPath="url(#__lottie_element_28)">
        <g
          style={{ display: 'block', transform: 'matrix(1,0,0,1,0,0)' }}
          opacity="1"
          className="rotate-adjust-and-back"
        >
          <g
            opacity="1"
            transform="matrix(0.8995603322982788,-0.8114914894104004,0.8114914894104004,0.8995603322982788,1.0572385787963867,13.542327880859375)"
          >
            <path
              fill="rgb(178,98,254)"
              fillOpacity="1"
              d=" M8.648597717285156,0.11783526837825775 C9.091397285461426,0.2144152671098709 9.200997352600098,0.7544552683830261 8.880497932434082,1.0749353170394897 C8.137197494506836,1.8182452917099 7.393897533416748,2.5615553855895996 6.65059757232666,3.3048653602600098 C5.901897430419922,4.053555488586426 5.901897430419922,5.267405033111572 6.65059757232666,6.0161051750183105 C7.399197578430176,6.764805316925049 8.613097190856934,6.764805316925049 9.361797332763672,6.0161051750183105 C10.105097770690918,5.2727952003479 10.848397254943848,4.52948522567749 11.591697692871094,3.78617525100708 C11.91219711303711,3.465695381164551 12.452197074890137,3.5752952098846436 12.548797607421875,4.018115043640137 C12.907397270202637,5.6623053550720215 12.44759750366211,7.449105262756348 11.169297218322754,8.727405548095703 C9.65219783782959,10.244504928588867 7.418797492980957,10.608805656433105 5.557697296142578,9.820205688476562 C4.796051025390625,10.581838607788086 4.034404277801514,11.34347152709961 3.2727575302124023,12.10510540008545 C2.5240674018859863,12.853805541992188 1.310207486152649,12.853805541992188 0.5615174770355225,12.10510540008545 C-0.18717250227928162,11.356505393981934 -0.18717250227928162,10.14260482788086 0.5615174770355225,9.393905639648438 C1.3231642246246338,8.632271766662598 2.084810733795166,7.870638370513916 2.8464574813842773,7.109005451202393 C2.0579075813293457,5.247905254364014 2.4221975803375244,3.014495372772217 3.939307451248169,1.497375249862671 C5.217597484588623,0.21908527612686157 7.004397392272949,-0.24075473845005035 8.648597717285156,0.11783526837825775 "
            ></path>
          </g>
        </g>
      </g>
    </svg>
  );
}
