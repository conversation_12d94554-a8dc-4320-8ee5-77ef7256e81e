name: Cleanup Container Registry

on:
  schedule:
    - cron: '0 0 * * *' # Run daily at midnight

jobs:
  cleanup:
    runs-on: ubuntu-latest

    steps:
      - name: Delete old container images
        uses: actions/delete-package-versions@v5
        with:
          package-name: ${{ github.repository }}
          package-type: 'container'
          min-versions-to-keep: 5
          delete-only-untagged-versions: true
