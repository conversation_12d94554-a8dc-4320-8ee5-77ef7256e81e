name: CD to Production

on:
  workflow_run:
    workflows: ["CD to Container Regestry"]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.event.workflow_run.conclusion == 'success'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install sshpass
        run: sudo apt-get install -y sshpass

      - name: Deploy to DigitalOcean
        env:
          host: ${{ secrets.DEPLOY_HOST }}
          user: ${{ secrets.DEPLOY_USER }}
          password: ${{ secrets.DEPLOY_PASSWORD }}
        run: |
          sshpass -p '${password}' ssh ${user}@${host} << EOF
          cd ~/kintu && \
          git pull origin main && \
          docker compose -f deploy-compose.yml down && \
          docker rmi $(docker images -q ghcr.io/brzempreendimentos/kintu:latest) && \
          docker compose -f deploy-compose.yml pull api && \
          docker compose -f deploy-compose.yml up -d
          EOF

